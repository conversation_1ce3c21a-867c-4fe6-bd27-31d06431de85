'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🔄 Starting bookmark and assignment separation migration...');
    
    // Step 1: Check if mo_recipe_bookmarks table exists
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('mo_recipe_bookmarks')) {
      throw new Error('mo_recipe_bookmarks table must be created first. Run the create-recipe-bookmarks migration.');
    }

    // Step 2: Get count of existing RecipeUser entries
    const [existingEntries] = await queryInterface.sequelize.query(
      'SELECT COUNT(*) as count FROM mo_recipe_user WHERE status = "active"'
    );
    const existingCount = existingEntries[0].count;
    console.log(`📊 Found ${existingCount} existing active RecipeUser entries`);

    if (existingCount > 0) {
      console.log('📝 Migration Strategy:');
      console.log('   - All existing mo_recipe_user entries will be preserved as ASSIGNMENTS');
      console.log('   - The new mo_recipe_bookmarks table starts empty');
      console.log('   - Users will need to re-bookmark recipes using the new bookmark system');
      console.log('   - This ensures no assignment data is lost during the separation');
    }

    // Step 3: Add a comment to the mo_recipe_user table to clarify its purpose
    await queryInterface.addColumn('mo_recipe_user', 'migration_note', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'Post-migration: This table is now used exclusively for recipe assignments. Bookmarks are handled by mo_recipe_bookmarks table.'
    });

    // Step 4: Update the migration_note for existing entries
    if (existingCount > 0) {
      await queryInterface.sequelize.query(`
        UPDATE mo_recipe_user 
        SET migration_note = 'Migrated: Originally mixed bookmark/assignment data, now assignment-only'
        WHERE migration_note IS NULL
      `);
    }

    // Step 5: Create a migration log entry
    await queryInterface.sequelize.query(`
      INSERT INTO mo_recipe_user (recipe_id, user_id, status, organization_id, created_by, updated_by, migration_note, created_at, updated_at)
      SELECT 
        -1 as recipe_id,
        -1 as user_id,
        'inactive' as status,
        'MIGRATION_LOG' as organization_id,
        1 as created_by,
        1 as updated_by,
        CONCAT('MIGRATION COMPLETED: Separated bookmarks and assignments. Preserved ', ${existingCount}, ' assignment entries. Bookmark table created empty.') as migration_note,
        NOW() as created_at,
        NOW() as updated_at
      WHERE NOT EXISTS (
        SELECT 1 FROM mo_recipe_user WHERE recipe_id = -1 AND user_id = -1 AND organization_id = 'MIGRATION_LOG'
      )
    `);

    console.log('✅ Migration completed successfully!');
    console.log('📋 Summary:');
    console.log(`   - Preserved ${existingCount} assignment entries in mo_recipe_user`);
    console.log('   - Created empty mo_recipe_bookmarks table for new bookmark system');
    console.log('   - Added migration tracking to mo_recipe_user table');
    console.log('');
    console.log('🔔 Important Notes:');
    console.log('   - All existing RecipeUser entries are now treated as ASSIGNMENTS only');
    console.log('   - Users will need to re-bookmark recipes using the new bookmark API');
    console.log('   - Bookmark and assignment operations are now completely independent');
  },

  async down(queryInterface, Sequelize) {
    console.log('⚠️  Rolling back bookmark and assignment separation...');
    
    // Remove the migration note column
    await queryInterface.removeColumn('mo_recipe_user', 'migration_note');
    
    // Remove migration log entry
    await queryInterface.sequelize.query(`
      DELETE FROM mo_recipe_user 
      WHERE recipe_id = -1 AND user_id = -1 AND organization_id = 'MIGRATION_LOG'
    `);

    console.log('✅ Rollback completed. Note: mo_recipe_bookmarks table is not dropped automatically.');
    console.log('   Run the drop-recipe-bookmarks migration separately if needed.');
  }
};
