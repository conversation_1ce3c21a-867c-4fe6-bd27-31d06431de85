'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('mo_recipe_bookmarks', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      recipe_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'mo_recipe',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive'),
        allowNull: false,
        defaultValue: 'active'
      },
      organization_id: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      updated_by: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('mo_recipe_bookmarks', {
      fields: ['recipe_id', 'user_id'],
      unique: true,
      name: 'unique_recipe_user_bookmark'
    });

    await queryInterface.addIndex('mo_recipe_bookmarks', {
      fields: ['organization_id'],
      name: 'idx_recipe_bookmarks_organization'
    });

    await queryInterface.addIndex('mo_recipe_bookmarks', {
      fields: ['status'],
      name: 'idx_recipe_bookmarks_status'
    });

    await queryInterface.addIndex('mo_recipe_bookmarks', {
      fields: ['created_by'],
      name: 'idx_recipe_bookmarks_created_by'
    });

    await queryInterface.addIndex('mo_recipe_bookmarks', {
      fields: ['updated_by'],
      name: 'idx_recipe_bookmarks_updated_by'
    });

    await queryInterface.addIndex('mo_recipe_bookmarks', {
      fields: ['user_id'],
      name: 'idx_recipe_bookmarks_user_id'
    });

    await queryInterface.addIndex('mo_recipe_bookmarks', {
      fields: ['recipe_id'],
      name: 'idx_recipe_bookmarks_recipe_id'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('mo_recipe_bookmarks');
  }
};
