{"enhanced_highlight_response": {"status": true, "message": "Recipe retrieved successfully", "data": {"id": 123, "recipe_title": "Vegan Chocolate Cake", "recipe_slug": "vegan-chocolate-cake", "highlight": {"component": "attributes", "action": "updated", "description": "Dietary attributes were updated", "lastModified": "2024-01-15T10:30:00Z", "old_value": {"dietary_attributes": [{"id": 17, "name": "Vegan", "slug": "vegan", "type": "dietary"}, {"id": 19, "name": "Gluten-Free", "slug": "gluten-free", "type": "dietary"}]}, "modifiedBy": {"userId": 456, "userName": "<PERSON>"}, "priority": 2, "context": {"fieldName": "dietary_attributes"}}}}, "attribute_details_api_response": {"status": true, "message": "Attribute details retrieved successfully", "data": {"attributes": [{"id": 17, "name": "Vegan", "slug": "vegan", "type": "dietary", "description": "Contains no animal products or by-products", "status": "active", "icon": {"id": 123, "name": "vegan-icon.svg", "location": "icons/dietary/vegan.svg", "mime_type": "image/svg+xml", "url": "https://your-domain.com/backend-api/v1/public/user/get-file?location=icons/dietary/vegan.svg"}}, {"id": 19, "name": "Gluten-Free", "slug": "gluten-free", "type": "dietary", "description": "Does not contain gluten or gluten-containing ingredients", "status": "active", "icon": {"id": 124, "name": "gluten-free-icon.svg", "location": "icons/dietary/gluten-free.svg", "mime_type": "image/svg+xml", "url": "https://your-domain.com/backend-api/v1/public/user/get-file?location=icons/dietary/gluten-free.svg"}}], "total": 2}}, "enhanced_category_structure": {"old_value": {"categories": [{"id": 5, "name": "Desserts", "slug": "desserts"}, {"id": 8, "name": "Healthy", "slug": "healthy"}]}}, "usage_examples": {"frontend_display": {"description": "Frontend can now display meaningful names instead of just IDs", "before": "Dietary attributes: [17, 19]", "after": "Dietary attributes: <PERSON>n, Gluten-Free"}, "api_usage": {"get_attribute_details": {"url": "GET /api/v1/private/recipes/attributes/details?attribute_ids=17,19,23", "description": "Get detailed information for multiple attributes at once"}, "recipe_with_highlights": {"url": "GET /api/v1/recipes/get-by-id/123", "description": "Recipe data now includes enhanced highlight information with attribute names"}}, "key_benefits": ["Frontend gets attribute names, not just IDs", "Consistent data structure across all APIs", "Backward compatibility with legacy ID-only data", "Rich metadata including icons and descriptions", "Better user experience with meaningful display names", "Efficient bulk attribute lookup API"]}}