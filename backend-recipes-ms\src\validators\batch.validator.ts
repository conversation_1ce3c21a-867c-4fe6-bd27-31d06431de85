import { celebrate, Joi, Segments } from "celebrate";
import { RecipeYieldUnit } from "../models/Recipe";

/**
 * Validate basic recipe information batch request
 */
const validateBasicInfoBatch = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_title: Joi.string().max(100).required().messages({
          "string.max": "Recipe title cannot exceed 100 characters",
          "any.required": "Recipe title is required",
        }),
        recipe_public_title: Joi.string().max(100).allow(null, "").optional().messages({
          "string.max": "Recipe public title cannot exceed 100 characters",
        }),
        recipe_description: Joi.string().allow(null, "").optional(),
        recipe_preparation_time: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional()
          .messages({
            "number.min": "Preparation time cannot be negative",
            "string.pattern.base": "Preparation time must be a valid number",
          }),
        recipe_cook_time: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional()
          .messages({
            "number.min": "Cook time cannot be negative",
            "string.pattern.base": "Cook time must be a valid number",
          }),
        has_recipe_public_visibility: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .default(false)
          .messages({
            "alternatives.match": "Public visibility must be a boolean (true/false) or string ('true'/'false')",
          }),
        has_recipe_private_visibility: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .default(false)
          .messages({
            "alternatives.match": "Private visibility must be a boolean (true/false) or string ('true'/'false')",
          }),
        recipe_status: Joi.string().valid("draft", "publish", "archived").optional(),
        recipe_complexity_level: Joi.string().valid("low", "medium", "hard").optional(),
        categories: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
        dietary_attributes: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
        recipe_id: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .optional()
      })
      .unknown(true), // Allow additional fields for flexibility
  });

/**
 * Validate ingredients and nutrition batch request
 */
const validateIngredientsNutritionBatch = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_id: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .required()
          .messages({
            "number.positive": "Recipe ID must be a positive number",
            "string.pattern.base": "Recipe ID must be a valid number",
            "any.required": "Recipe ID is required",
          }),
        ingredients: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().required(),
                quantity: Joi.number().min(0).allow(null).optional(),
                measure: Joi.number().allow(null).optional(),
                wastage: Joi.number().min(0).max(100).allow(null).optional(),
                cost: Joi.number().min(0).allow(null).optional(),
                cooking_method: Joi.number().allow(null).optional(),
                preparation_method: Joi.number().allow(null).optional(),
              })
            ),
            Joi.string()
          )
          .optional(),
        nutrition_attributes: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().required(),
                unit_of_measure: Joi.string()
                  .max(100)
                  .allow(null, "")
                  .optional(),
                unit: Joi.number().allow(null).optional(),
                description: Joi.string().allow(null, "").optional(),
              })
            ),
            Joi.string()
          )
          .optional(),
        allergen_attributes: Joi.alternatives()
          .try(
            Joi.object({
              contains: Joi.array().items(Joi.number().positive()).optional(),
              may_contain: Joi.array().items(Joi.number().positive()).optional(),
            }),
            Joi.string()
          )
          .optional(),
        cuisine_attributes: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
        dietary_attributes: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
        haccp_attributes: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().integer().positive().required(),
                attribute_description: Joi.string().allow(null, "").optional(),
                use_default: Joi.alternatives()
                  .try(Joi.boolean(), Joi.string().valid("true", "false"))
                  .optional()
                  .default(false),
              })
            ).description("HACCP attributes array - allows multiple entries with same ID for different control points"),
            Joi.string()
          )
          .optional(),
        is_ingredient_cooking_method: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
        is_preparation_method: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
        is_cost_manual: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
        // Serving details
        recipe_serve_in: Joi.string().allow(null, "").optional(),
        recipe_garnish: Joi.string().allow(null, "").optional(),
        recipe_head_chef_tips: Joi.string().allow(null, "").optional(),
        recipe_foh_tips: Joi.string().allow(null, "").optional(),
        recipe_impression: Joi.string().allow(null, "").optional(),
        recipe_yield: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+(\.\d+)?$/))
          .allow(null)
          .optional(),
        recipe_yield_unit: Joi.string()
          .valid(...Object.values(RecipeYieldUnit))
          .allow(null, "")
          .optional(),
        recipe_total_portions: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        recipe_single_portion_size: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+(\.\d+)?$/))
          .allow(null)
          .optional(),
        recipe_serving_method: Joi.string().allow(null, "").optional(),
        recipe_status: Joi.string().valid("draft", "publish", "archived").optional()
      })
      .unknown(true), // Allow additional fields for flexibility
  });

/**
 * Validate recipe steps request
 */
const validateStepsBatch = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_id: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .required()
          .messages({
            "number.positive": "Recipe ID must be a positive number",
            "string.pattern.base": "Recipe ID must be a valid number",
            "any.required": "Recipe ID is required",
          }),
        recipe_steps: Joi.array()
          .items(
            Joi.object({
              recipe_step_order: Joi.number().min(1).required(),
              recipe_step_description: Joi.string().allow(null, "").optional(),
              item_id: Joi.number().allow(null).optional(),
            })
          )
          .min(1)
          .required()
          .messages({
            "array.min": "At least one step is required",
            "any.required": "Steps must be a valid array",
          }),
        recipe_status: Joi.string().valid("draft", "publish", "archived").optional()
      })
      .unknown(true), // Allow additional fields for flexibility
  });

/**
 * Validate recipe file association request
 */
const validateUploadsBatch = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_id: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .required()
          .messages({
            "number.positive": "Recipe ID must be a positive number",
            "string.pattern.base": "Recipe ID must be a valid number",
            "any.required": "Recipe ID is required",
          }),
        recipe_resources: Joi.alternatives()
          .try(
            // Simple array of item IDs (for backward compatibility)
            Joi.array().items(Joi.number().positive()),
            // Array of resource objects (matching create recipe API)
            Joi.array().items(
              Joi.object({
                type: Joi.string().valid("item", "link").optional().default("item"),
                item_id: Joi.alternatives()
                  .try(
                    Joi.number().positive(),
                    Joi.string().pattern(/^\d+$/)
                  )
                  .when("type", {
                    is: "item",
                    then: Joi.required(),
                    otherwise: Joi.allow(null),
                  }),
                item_link: Joi.string().uri().allow(null, "").when("type", {
                  is: "link",
                  then: Joi.required(),
                  otherwise: Joi.optional(),
                }),
                item_link_type: Joi.string()
                  .valid("image", "video", "audio", "pdf", "document", "youtube", "link", "other", "text")
                  .when("type", {
                    is: "link",
                    then: Joi.required(),
                    otherwise: Joi.allow(null),
                  }),
              })
            ),
            Joi.string()
          )
          .required()
          .messages({
            "array.base": "recipe_resources must be an array",
            "any.required": "recipe_resources is required",
            "number.positive": "Each item ID must be a positive number",
          }),
        recipe_placeholder: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        recipe_status: Joi.string().valid("draft", "publish", "archived").optional()
      })
      .unknown(false), // Don't allow unknown fields for this simplified API
  });


/**
 * Validate multiple files upload request
 */
const validateMultipleFilesUpload = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        // Recipe upload parameters
        uploadType: Joi.string()
          .valid(
            "recipePlaceholder", "recipeImage", "recipeFiles", "recipeDocument",
            "stepImages", "stepMedia", "recipeVideo", "recipeAudio",
            "recipeThumbnail", "nutritionLabel"
          )
          .optional()
          .messages({
            "string.valid": "uploadType must be one of: recipePlaceholder, recipeImage, recipeFiles, recipeDocument, stepImages, stepMedia, recipeVideo, recipeAudio, recipeThumbnail, nutritionLabel"
          }),
        recipeId: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .optional()
          .messages({
            "number.positive": "Recipe ID must be a positive number",
            "string.pattern.base": "Recipe ID must be a valid number"
          }),
        stepNumbers: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional()
          .messages({
            "array.base": "stepNumbers must be an array of positive numbers",
            "number.positive": "Each step number must be a positive number"
          }),

        // General upload parameters (backward compatibility)
        file_type: Joi.string().valid("image", "document", "video", "audio").optional(),
        description: Joi.string().max(255).optional(),
        max_files: Joi.number().integer().min(1).max(20).optional().default(10),
      })
      .unknown(true), // Allow file upload and other fields
  });



/**
 * Validate bulk delete files request
 */
const validateBulkDeleteFiles = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_resources: Joi.array()
          .items(Joi.number().positive())
          .min(1)
          .required()
          .messages({
            "array.base": "recipe_resources must be an array",
            "array.min": "At least one recipe_resources ID is required",
            "any.required": "recipe_resources is required",
            "number.positive": "Each recipe_resources ID must be a positive number",
          }),
        recipe_id: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .required()
          .messages({
            "number.positive": "Recipe ID must be a positive number",
            "string.pattern.base": "Recipe ID must be a valid number",
            "any.required": "Recipe ID is required",
          }),
      })
      .unknown(true), // Don't allow unknown fields
  });



// Default export object following the pattern of other validators
export default {
  validateBasicInfoBatch,
  validateIngredientsNutritionBatch,
  validateStepsBatch,
  validateUploadsBatch,
  validateMultipleFilesUpload,
  validateBulkDeleteFiles,
};
