const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log("🚀 Starting bookmark and assignment separation migration...");

      // Step 1: Create mo_recipe_bookmark table
      console.log("📋 Creating mo_recipe_bookmark table...");
      await queryInterface.createTable('mo_recipe_bookmark', {
        recipe_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'mo_recipe',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'users',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        status: {
          type: Sequelize.ENUM('active', 'inactive'),
          allowNull: false,
          defaultValue: 'active',
        },
        organization_id: {
          type: Sequelize.STRING(100),
          allowNull: true,
        },
        created_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        updated_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
      }, { transaction });

      // Step 2: Create mo_recipe_assign table
      console.log("📋 Creating mo_recipe_assign table...");
      await queryInterface.createTable('mo_recipe_assign', {
        recipe_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'mo_recipe',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'users',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        status: {
          type: Sequelize.ENUM('active', 'inactive'),
          allowNull: false,
          defaultValue: 'active',
        },
        organization_id: {
          type: Sequelize.STRING(100),
          allowNull: true,
        },
        created_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        updated_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
      }, { transaction });

      // Step 3: Add indexes for mo_recipe_bookmark
      console.log("📋 Adding indexes for mo_recipe_bookmark...");
      await queryInterface.addIndex('mo_recipe_bookmark', ['recipe_id', 'user_id'], {
        unique: true,
        name: 'unique_recipe_bookmark',
        transaction
      });
      await queryInterface.addIndex('mo_recipe_bookmark', ['organization_id'], {
        name: 'idx_recipe_bookmark_organization',
        transaction
      });
      await queryInterface.addIndex('mo_recipe_bookmark', ['status'], {
        name: 'idx_recipe_bookmark_status',
        transaction
      });
      await queryInterface.addIndex('mo_recipe_bookmark', ['created_by'], {
        name: 'idx_recipe_bookmark_created_by',
        transaction
      });
      await queryInterface.addIndex('mo_recipe_bookmark', ['updated_by'], {
        name: 'idx_recipe_bookmark_updated_by',
        transaction
      });

      // Step 4: Add indexes for mo_recipe_assign
      console.log("📋 Adding indexes for mo_recipe_assign...");
      await queryInterface.addIndex('mo_recipe_assign', ['recipe_id', 'user_id'], {
        unique: true,
        name: 'unique_recipe_assign',
        transaction
      });
      await queryInterface.addIndex('mo_recipe_assign', ['organization_id'], {
        name: 'idx_recipe_assign_organization',
        transaction
      });
      await queryInterface.addIndex('mo_recipe_assign', ['status'], {
        name: 'idx_recipe_assign_status',
        transaction
      });
      await queryInterface.addIndex('mo_recipe_assign', ['created_by'], {
        name: 'idx_recipe_assign_created_by',
        transaction
      });
      await queryInterface.addIndex('mo_recipe_assign', ['updated_by'], {
        name: 'idx_recipe_assign_updated_by',
        transaction
      });

      // Step 5: Migrate existing data from mo_recipe_user
      console.log("📋 Migrating existing data...");
      
      // Check if mo_recipe_user table exists and has data
      const existingData = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM mo_recipe_user WHERE status = 'active'`,
        { type: QueryTypes.SELECT, transaction }
      );

      const dataCount = existingData[0]?.count || 0;
      console.log(`📊 Found ${dataCount} active records in mo_recipe_user`);

      if (dataCount > 0) {
        // For now, we'll treat all existing mo_recipe_user entries as bookmarks
        // since the current system primarily uses this table for bookmarks
        // Assignments will be created fresh through the new assignment API
        console.log("📋 Migrating all existing records as bookmarks...");
        
        await queryInterface.sequelize.query(`
          INSERT INTO mo_recipe_bookmark 
          (recipe_id, user_id, status, organization_id, created_by, updated_by, created_at, updated_at)
          SELECT recipe_id, user_id, status, organization_id, created_by, updated_by, created_at, updated_at
          FROM mo_recipe_user
        `, { transaction });

        console.log("✅ Data migration completed");
      }

      await transaction.commit();
      console.log("✅ Bookmark and assignment separation migration completed successfully!");
      console.log("📝 Note: All existing mo_recipe_user data has been migrated to mo_recipe_bookmark");
      console.log("📝 Recipe assignments should be set up fresh using the new assignment API");

    } catch (error) {
      await transaction.rollback();
      console.error("❌ Error during migration:", error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log("🔄 Rolling back bookmark and assignment separation...");

      // Step 1: Migrate data back to mo_recipe_user if needed
      console.log("📋 Checking for data to migrate back...");
      
      const bookmarkCount = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM mo_recipe_bookmark`,
        { type: QueryTypes.SELECT, transaction }
      );

      const assignmentCount = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM mo_recipe_assign`,
        { type: QueryTypes.SELECT, transaction }
      );

      console.log(`📊 Found ${bookmarkCount[0]?.count || 0} bookmarks and ${assignmentCount[0]?.count || 0} assignments`);

      // Migrate bookmarks back to mo_recipe_user
      if (bookmarkCount[0]?.count > 0) {
        console.log("📋 Migrating bookmarks back to mo_recipe_user...");
        await queryInterface.sequelize.query(`
          INSERT IGNORE INTO mo_recipe_user 
          (recipe_id, user_id, status, organization_id, created_by, updated_by, created_at, updated_at)
          SELECT recipe_id, user_id, status, organization_id, created_by, updated_by, created_at, updated_at
          FROM mo_recipe_bookmark
        `, { transaction });
      }

      // Note: Assignments will be lost in rollback since they were separate functionality
      if (assignmentCount[0]?.count > 0) {
        console.log("⚠️  Warning: Recipe assignments will be lost during rollback");
      }

      // Step 2: Drop the new tables
      console.log("📋 Dropping mo_recipe_assign table...");
      await queryInterface.dropTable('mo_recipe_assign', { transaction });

      console.log("📋 Dropping mo_recipe_bookmark table...");
      await queryInterface.dropTable('mo_recipe_bookmark', { transaction });

      await transaction.commit();
      console.log("✅ Rollback completed successfully!");
      console.log("⚠️  Note: Any recipe assignments created after migration have been lost");

    } catch (error) {
      await transaction.rollback();
      console.error("❌ Error during rollback:", error);
      throw error;
    }
  }
};
